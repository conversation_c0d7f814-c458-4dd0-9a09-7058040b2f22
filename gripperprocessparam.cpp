#include "gripperprocessparam.h"
#include "ui_gripperprocessparam.h"

GripperProcessParam::GripperProcessParam(QWidget *parent) :
    BindableWidget(parent),  // 继承BindableWidget
    ui(new Ui::GripperProcessParam)
{
    ui->setupUi(this);

    // 初始化数据绑定
    initializeBinding();

    qDebug() << "GripperProcessParam: 夹爪参数界面初始化完成，已启用自动数据绑定";
}

GripperProcessParam::~GripperProcessParam()
{
    delete ui;
}

void GripperProcessParam::onDataChanged(int index, const QString& name,
                                       const QVariant& newValue, const QVariant& oldValue)
{
    qDebug() << QString("GripperProcessParam: 参数变化 [%1] %2: %3 -> %4")
                .arg(index)
                .arg(name)
                .arg(oldValue.toString())
                .arg(newValue.toString());

    // 发射参数变化信号
    emit parameterChanged(index, name, newValue, oldValue);
}

void GripperProcessParam::onBindingInitialized(int controlCount, const QList<ControlBindInfo>& controls)
{
    qDebug() << "GripperProcessParam: 数据绑定初始化完成，绑定了" << controlCount << "个控件";

    for (const ControlBindInfo& info : controls) {
        qDebug() << QString("  [%1] %2 (%3) = %4")
                    .arg(info.index)
                    .arg(info.name)
                    .arg(info.type)
                    .arg(info.value.toString());
    }
}
