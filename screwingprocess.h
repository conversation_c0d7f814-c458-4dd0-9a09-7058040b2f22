#ifndef SCREWINGPROCESS_H
#define SCREWINGPROCESS_H

#include <QWidget>
#include <QVariant>
#include <QDebug>
#include "framework/bindable_widget.h"

namespace Ui {
class ScrewingProcess;
}

/**
 * @brief 拧紧工艺界面 - 继承BindableWidget实现自动数据绑定
 */
class ScrewingProcess : public BindableWidget
{
    Q_OBJECT

public:
    explicit ScrewingProcess(QWidget *parent = nullptr);
    ~ScrewingProcess();

    /**
     * @brief 获取UI指针
     */
    Ui::ScrewingProcess* getUI() const { return ui; }

protected:
    /**
     * @brief 重写数据变化处理函数
     */
    void onDataChanged(int index, const QString& name,
                      const QVariant& newValue, const QVariant& oldValue) override;

    /**
     * @brief 重写绑定初始化完成处理函数
     */
    void onBindingInitialized(int controlCount, const QList<ControlBindInfo>& controls) override;

signals:
    /**
     * @brief 拧紧参数发生变化
     */
    void screwingParameterChanged(int index, const QString& name,
                                 const QVariant& value, const QVariant& oldValue);

private:
    /**
     * @brief 处理特定的拧紧参数变化
     */
    void handleScrewingParameterChange(const QString& name, const QVariant& value);

private:
    Ui::ScrewingProcess *ui;
};

#endif // SCREWINGPROCESS_H
