#ifndef SCREWINGPROCESSPARAM_H
#define SCREWINGPROCESSPARAM_H

#include <QWidget>
#include <QVariant>
#include <QDebug>
#include "framework/bindable_widget.h"

namespace Ui {
class ScrewingProcessParam;
}

/**
 * @brief 拧紧参数界面 - 继承BindableWidget实现自动数据绑定
 */
class ScrewingProcessParam : public BindableWidget
{
    Q_OBJECT

public:
    explicit ScrewingProcessParam(QWidget *parent = nullptr);
    ~ScrewingProcessParam();

    /**
     * @brief 获取UI指针
     */
    Ui::ScrewingProcessParam* getUI() const { return ui; }

protected:
    /**
     * @brief 重写数据变化处理函数
     */
    void onDataChanged(int index, const QString& name,
                      const QVariant& newValue, const QVariant& oldValue) override;

    /**
     * @brief 重写绑定初始化完成处理函数
     */
    void onBindingInitialized(int controlCount, const QList<ControlBindInfo>& controls) override;

signals:
    /**
     * @brief 拧紧参数发生变化
     */
    void parameterChanged(int index, const QString& name,
                         const QVariant& value, const QVariant& oldValue);

private:
    Ui::ScrewingProcessParam *ui;
};

#endif // SCREWINGPROCESSPARAM_H
