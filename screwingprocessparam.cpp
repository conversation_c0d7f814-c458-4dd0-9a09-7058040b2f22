#include "screwingprocessparam.h"
#include "ui_screwingprocessparam.h"

ScrewingProcessParam::ScrewingProcessParam(QWidget *parent) :
    BindableWidget(parent),  // 继承BindableWidget
    ui(new Ui::ScrewingProcessParam)
{
    ui->setupUi(this);

    // 初始化数据绑定
    initializeBinding();

    qDebug() << "ScrewingProcessParam: 拧紧参数界面初始化完成，已启用自动数据绑定";
}

ScrewingProcessParam::~ScrewingProcessParam()
{
    delete ui;
}

void ScrewingProcessParam::onDataChanged(int index, const QString& name,
                                        const QVariant& newValue, const QVariant& oldValue)
{
    qDebug() << QString("ScrewingProcessParam: 参数变化 [%1] %2: %3 -> %4")
                .arg(index)
                .arg(name)
                .arg(oldValue.toString())
                .arg(newValue.toString());

    // 发射参数变化信号
    emit parameterChanged(index, name, newValue, oldValue);
}

void ScrewingProcessParam::onBindingInitialized(int controlCount, const QList<ControlBindInfo>& controls)
{
    qDebug() << "ScrewingProcessParam: 数据绑定初始化完成，绑定了" << controlCount << "个控件";

    for (const ControlBindInfo& info : controls) {
        qDebug() << QString("  [%1] %2 (%3) = %4")
                    .arg(info.index)
                    .arg(info.name)
                    .arg(info.type)
                    .arg(info.value.toString());
    }
}
