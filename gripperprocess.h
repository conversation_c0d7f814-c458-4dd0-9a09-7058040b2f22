#ifndef GRIPPERPROCESS_H
#define GRIPPERPROCESS_H

#include <QWidget>
#include <QVariant>
#include <QDebug>
#include "framework/bindable_widget.h"

namespace Ui {
class GripperProcess;
}

/**
 * @brief 夹爪工艺界面 - 继承BindableWidget实现自动数据绑定
 *
 * 这个类演示了如何将现有的Qt Designer界面直接改造为继承BindableWidget，
 * 从而自动获得数据绑定功能：
 * 1. 自动扫描并绑定所有控件
 * 2. 控件数据变化时自动通知外部
 * 3. 支持外部通过序号或名称更新控件数据
 */
class GripperProcess : public BindableWidget
{
    Q_OBJECT

public:
    explicit GripperProcess(QWidget *parent = nullptr);
    ~GripperProcess();

    /**
     * @brief 获取UI指针（用于访问Qt Designer生成的控件）
     * @return UI指针
     */
    Ui::GripperProcess* getUI() const { return ui; }

protected:
    /**
     * @brief 重写数据变化处理函数
     * 当任何绑定的控件数据发生变化时会自动调用
     */
    void onDataChanged(int index, const QString& name,
                      const QVariant& newValue, const QVariant& oldValue) override;

    /**
     * @brief 重写绑定初始化完成处理函数
     * 当绑定初始化完成时会自动调用
     */
    void onBindingInitialized(int controlCount, const QList<ControlBindInfo>& controls) override;

signals:
    /**
     * @brief 夹爪参数发生变化
     * @param index 参数索引
     * @param name 参数名称
     * @param value 新值
     * @param oldValue 旧值
     */
    void gripperParameterChanged(int index, const QString& name,
                                const QVariant& value, const QVariant& oldValue);

    /**
     * @brief 夹爪界面初始化完成
     * @param controlCount 绑定的控件数量
     */
    void gripperInterfaceInitialized(int controlCount);

private:
    /**
     * @brief 处理特定的夹爪参数变化
     * @param name 参数名称
     * @param value 新值
     */
    void handleGripperParameterChange(const QString& name, const QVariant& value);

private:
    Ui::GripperProcess *ui;
};

#endif // GRIPPERPROCESS_H
