#include "gripperprocess.h"
#include "ui_gripperprocess.h"

GripperProcess::GripperProcess(QWidget *parent) :
    BindableWidget(parent),  // 继承BindableWidget而不是QWidget
    ui(new Ui::GripperProcess)
{
    ui->setupUi(this);

    // 初始化数据绑定（关键步骤！）
    initializeBinding();

    qDebug() << "GripperProcess: 夹爪界面初始化完成，已启用自动数据绑定";
}

GripperProcess::~GripperProcess()
{
    delete ui;
}

void GripperProcess::onDataChanged(int index, const QString& name,
                                  const QVariant& newValue, const QVariant& oldValue)
{
    // 输出数据变化信息
    qDebug() << QString("GripperProcess: 参数变化 [%1] %2: %3 -> %4")
                .arg(index)
                .arg(name)
                .arg(oldValue.toString())
                .arg(newValue.toString());

    // 处理特定的夹爪参数变化
    handleGripperParameterChange(name, newValue);

    // 发射夹爪参数变化信号给外部
    emit gripperParameterChanged(index, name, newValue, oldValue);
}

void GripperProcess::onBindingInitialized(int controlCount, const QList<ControlBindInfo>& controls)
{
    qDebug() << "GripperProcess: 数据绑定初始化完成，绑定了" << controlCount << "个控件:";

    // 输出所有绑定的控件信息
    for (const ControlBindInfo& info : controls) {
        qDebug() << QString("  [%1] %2 (%3) = %4")
                    .arg(info.index)
                    .arg(info.name)
                    .arg(info.type)
                    .arg(info.value.toString());
    }

    // 发射初始化完成信号
    emit gripperInterfaceInitialized(controlCount);
}

void GripperProcess::handleGripperParameterChange(const QString& name, const QVariant& value)
{
    // 根据参数名称进行特定的处理
    if (name.contains("force", Qt::CaseInsensitive)) {
        // 处理力度参数变化
        double force = value.toDouble();
        qDebug() << "GripperProcess: 夹爪力度设置为" << force << "N";

        // 这里可以添加力度相关的业务逻辑
        // 比如：检查力度范围、更新状态显示等
    }
    else if (name.contains("position", Qt::CaseInsensitive)) {
        // 处理位置参数变化
        double position = value.toDouble();
        qDebug() << "GripperProcess: 夹爪位置设置为" << position << "mm";

        // 这里可以添加位置相关的业务逻辑
    }
    else if (name.contains("speed", Qt::CaseInsensitive)) {
        // 处理速度参数变化
        double speed = value.toDouble();
        qDebug() << "GripperProcess: 夹爪速度设置为" << speed << "mm/s";

        // 这里可以添加速度相关的业务逻辑
    }
    else if (name.contains("enable", Qt::CaseInsensitive)) {
        // 处理启用状态变化
        bool enabled = value.toBool();
        qDebug() << "GripperProcess: 夹爪" << (enabled ? "启用" : "禁用");

        // 这里可以添加启用状态相关的业务逻辑
    }

    // 可以在这里添加更多的参数处理逻辑
    // 比如：
    // - 发送参数到硬件设备
    // - 更新界面状态显示
    // - 保存参数到配置文件
    // - 触发其他相关操作
}
