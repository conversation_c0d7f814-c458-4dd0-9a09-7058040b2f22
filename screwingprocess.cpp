#include "screwingprocess.h"
#include "ui_screwingprocess.h"

ScrewingProcess::ScrewingProcess(QWidget *parent) :
    BindableWidget(parent),  // 继承BindableWidget
    ui(new Ui::ScrewingProcess)
{
    ui->setupUi(this);

    // 初始化数据绑定
    initializeBinding();

    qDebug() << "ScrewingProcess: 拧紧工艺界面初始化完成，已启用自动数据绑定";
}

ScrewingProcess::~ScrewingProcess()
{
    delete ui;
}

void ScrewingProcess::onDataChanged(int index, const QString& name,
                                   const QVariant& newValue, const QVariant& oldValue)
{
    qDebug() << QString("ScrewingProcess: 参数变化 [%1] %2: %3 -> %4")
                .arg(index)
                .arg(name)
                .arg(oldValue.toString())
                .arg(newValue.toString());

    // 处理特定的拧紧参数变化
    handleScrewingParameterChange(name, newValue);

    // 发射拧紧参数变化信号
    emit screwingParameterChanged(index, name, newValue, oldValue);
}

void ScrewingProcess::onBindingInitialized(int controlCount, const QList<ControlBindInfo>& controls)
{
    qDebug() << "ScrewingProcess: 数据绑定初始化完成，绑定了" << controlCount << "个控件";

    for (const ControlBindInfo& info : controls) {
        qDebug() << QString("  [%1] %2 (%3) = %4")
                    .arg(info.index)
                    .arg(info.name)
                    .arg(info.type)
                    .arg(info.value.toString());
    }
}

void ScrewingProcess::handleScrewingParameterChange(const QString& name, const QVariant& value)
{
    // 根据参数名称进行特定的处理
    if (name.contains("torque", Qt::CaseInsensitive)) {
        // 处理扭矩参数变化
        double torque = value.toDouble();
        qDebug() << "ScrewingProcess: 拧紧扭矩设置为" << torque << "Nm";
    }
    else if (name.contains("angle", Qt::CaseInsensitive)) {
        // 处理角度参数变化
        double angle = value.toDouble();
        qDebug() << "ScrewingProcess: 拧紧角度设置为" << angle << "度";
    }
    else if (name.contains("speed", Qt::CaseInsensitive)) {
        // 处理速度参数变化
        double speed = value.toDouble();
        qDebug() << "ScrewingProcess: 拧紧速度设置为" << speed << "rpm";
    }

    // 可以在这里添加更多的拧紧参数处理逻辑
}
